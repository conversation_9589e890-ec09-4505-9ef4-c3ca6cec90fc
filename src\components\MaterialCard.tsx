import React, { useState, useRef, useEffect } from 'react';
import { MoreVertical, Edit, Trash, BookOpen } from 'lucide-react';
import MaterialAttachment from './MaterialAttachment';

interface MaterialCardProps {
  id?: string;
  title: string;
  description?: string;
  attachments?: {
    type: 'drive' | 'youtube' | 'link' | 'file' | 'document';
    name: string;
    url: string;
    thumbnail?: string;
    fileId?: number;
    size?: number;
    uploadDate?: string;
  }[];
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

const MaterialCard: React.FC<MaterialCardProps> = ({
  id,
  title,
  description,
  attachments = [],
  onEdit,
  onDelete
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  
  // Get user role from localStorage or sessionStorage
  const userRole = localStorage.getItem('user_role') || sessionStorage.getItem('user_role');
  const isStudent = userRole?.toLowerCase() === 'student';
  // We only need to check if the user is a student to determine permissions
  
  // Check if we have a valid ID
  const hasValidId = !!id && (typeof id === 'string' || typeof id === 'number');
  
  // Log the ID for debugging purposes
  console.log(`Material Card ID: ${id}, Valid: ${hasValidId}`);
  
  // For string IDs, ensure we can extract a numeric part if needed
  const extractNumericId = () => {
    if (!id) return null;
    if (!isNaN(Number(id))) return id;
    const match = id.toString().match(/\d+/);
    return match ? match[0] : null;
  };

  const handleEdit = () => {
    if (onEdit && hasValidId) {
      onEdit(id);
    }
    setShowMenu(false);
  };

  const handleDeleteClick = () => {
    // Only attempt deletion if we have a valid ID
    if (!hasValidId) {
      console.error('Cannot delete material: Missing or invalid ID');
      alert('This material cannot be deleted because it doesn\'t have a valid ID.');
      setShowMenu(false);
      return;
    }
    
    setShowMenu(false);
    setShowDeleteConfirmation(true);
  };

  const confirmDelete = () => {
    // Double-check that we have both a valid ID and delete handler
    if (!hasValidId || !id) {
      console.error('Cannot delete material: Missing or invalid ID');
      setShowDeleteConfirmation(false);
      return;
    }
    
    if (!onDelete) {
      console.error('Cannot delete material: Delete handler not provided');
      setShowDeleteConfirmation(false);
      return;
    }
    
    try {
      // Use the extracted numeric ID if helpful for API
      const numericId = extractNumericId();
      console.log(`Deleting material with ID: ${id}, extracted ID: ${numericId}`);
      onDelete(id);
    } catch (error) {
      console.error('Error when attempting to delete material:', error);
    }
    
    setShowDeleteConfirmation(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <>
      <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 shadow-sm hover:shadow-md transition-shadow" id={`material-${id}`}>
        <div className="flex justify-between items-start mb-2">
          <div className="flex items-start gap-2 sm:gap-3">
            <BookOpen size={20} className="text-[#2563eb] mt-1 sm:w-6 sm:h-6" />
            <div>
              <h3 className="text-sm sm:text-base md:text-[16px] font-medium text-[#3c4043]">{title}</h3>
              {description && (
                <p className="text-xs sm:text-sm md:text-[14px] text-[#5f6368] mt-1">
                  {description.length > 100 ? `${description.substring(0, 100)}...` : description}
                </p>
              )}
              
              {/* Attachments */}
              {attachments.length > 0 && (
                <div className="mt-3 space-y-2">
                  {attachments.map((attachment, index) => (
                    <MaterialAttachment 
                      key={index} 
                      attachment={attachment} 
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
          
          {/* Only show three-dot menu for teachers */}
          {!isStudent && (
            <div className="relative">
              {showMenu && (
                <div ref={menuRef} className="absolute right-0 bottom-full mb-2 w-48 bg-white border border-gray-200 rounded-xl shadow-2xl z-20 animate-fade-in">
                  {/* Caret */}
                  <div className="absolute right-4 -bottom-2 w-3 h-3 bg-white border-l border-t border-gray-200 rotate-45 z-30"></div>
                  <button 
                    onClick={handleEdit}
                    className="w-full px-4 py-2 flex items-center gap-2 hover:bg-gray-50 text-sm text-left rounded-t-xl"
                  >
                    <Edit size={16} className="text-[#5f6368]" />
                    Edit
                  </button>
                  {hasValidId && onDelete && (
                    <button 
                      onClick={handleDeleteClick}
                      className="w-full px-4 py-2 flex items-center gap-2 hover:bg-gray-50 text-sm text-left text-red-600 rounded-b-xl"
                    >
                      <Trash size={16} />
                      Delete
                    </button>
                  )}
                </div>
              )}
              <button 
                onClick={() => setShowMenu(!showMenu)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <MoreVertical size={20} className="text-[#5f6368]" />
              </button>
            </div>
          )}
        </div>
      </div>
      
      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Delete material</h3>
            <p className="text-sm text-gray-500 mb-6">Are you sure you want to delete "{title}"? This action cannot be undone.</p>
            <div className="flex justify-end gap-3">
              <button 
                onClick={() => setShowDeleteConfirmation(false)}
                className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
              >
                Cancel
              </button>
              <button 
                onClick={confirmDelete}
                className="px-4 py-2 text-sm bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MaterialCard;