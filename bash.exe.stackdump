Stack trace:
Frame         Function      Args
0007FFFFA900  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9800) msys-2.0.dll+0x1FE8E
0007FFFFA900  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABD8) msys-2.0.dll+0x67F9
0007FFFFA900  000210046832 (000210286019, 0007FFFFA7B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA900  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA900  000210068E24 (0007FFFFA910, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABE0  00021006A225 (0007FFFFA910, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB39460000 ntdll.dll
7FFB388B0000 KERNEL32.DLL
7FFB36C70000 KERNELBASE.dll
7FFB38DC0000 USER32.dll
7FFB36B80000 win32u.dll
7FFB38D90000 GDI32.dll
7FFB36770000 gdi32full.dll
7FFB368B0000 msvcp_win.dll
7FFB37040000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB387F0000 advapi32.dll
7FFB39370000 msvcrt.dll
7FFB381B0000 sechost.dll
7FFB38090000 RPCRT4.dll
7FFB35D00000 CRYPTBASE.DLL
7FFB36AE0000 bcryptPrimitives.dll
7FFB377B0000 IMM32.DLL
