import { useState, useContext, useEffect, useCallback } from 'react';
import { FileText, Plus, HelpCircle, X, ChevronDown, ChevronUp, Users, Upload, Link as LinkIcon } from 'lucide-react';
import { useLocation, useParams } from 'react-router-dom';
import SelectClassModal from '../components/SelectClassModal';
import ReusePostModal from '../components/ReusePostModal';
import AddTopicModal from '../components/AddTopicModal';
import AssignmentModal from '../components/AssignmentModal';
import MaterialModal from '../components/MaterialModal';
import AssignmentCard from '../components/AssignmentCard';
import MaterialCard from '../components/MaterialCard';
import { ClassDataContext } from './ClassPage';
import { AssignmentData } from '../components/AssignmentModal';
import { MaterialData } from '../components/MaterialModal';
import { Assignment, getClassAssignments, deleteAssignment } from '../types/assignment';
import { Material, getClassMaterials, deleteMaterial } from '../types/material';

const ClassworkPage = () => {
  const classData = useContext(ClassDataContext);
  const location = useLocation();
  const [isCreateMenuOpen, setIsCreateMenuOpen] = useState(false);
  const [showAssignmentForm, setShowAssignmentForm] = useState(false);
  const [showQuizForm, setShowQuizForm] = useState(false);
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  const [showMaterialForm, setShowMaterialForm] = useState(false);
  const [showSelectClassModal, setShowSelectClassModal] = useState(false);
  const [showReusePostModal, setShowReusePostModal] = useState(false);
  const [showAddTopicModal, setShowAddTopicModal] = useState(false);
  const [studentsCanReply, setStudentsCanReply] = useState(true);
  const [studentsCanEdit, setStudentsCanEdit] = useState(false);
  const { classId } = useParams<{ classId: string }>();
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  
  // State for editing
  const [currentEditingAssignment, setCurrentEditingAssignment] = useState<string | null>(null);
  const [currentEditingMaterial, setCurrentEditingMaterial] = useState<string | null>(null);

  // --- Topic toggle state ---
  const [expandedTopics, setExpandedTopics] = useState<{ [key: string]: boolean }>({});

  // Load assignments and materials for this class
  useEffect(() => {
    if (classId) {
      loadAssignments();
      loadMaterials();
      
      // Listen for assignment updates
      const handleAssignmentUpdate = () => {
        loadAssignments();
      };
      
      // Listen for material updates
      const handleMaterialUpdate = () => {
        loadMaterials();
      };
      
      // Add event listeners
      window.addEventListener('assignmentUpdated', handleAssignmentUpdate);
      window.addEventListener('assignmentDeleted', handleAssignmentUpdate);
      window.addEventListener('newAssignmentCreated', handleAssignmentUpdate);
      
      window.addEventListener('materialUpdated', handleMaterialUpdate);
      window.addEventListener('materialDeleted', handleMaterialUpdate);
      window.addEventListener('newMaterialCreated', handleMaterialUpdate);
      
      return () => {
        // Remove event listeners
        window.removeEventListener('assignmentUpdated', handleAssignmentUpdate);
        window.removeEventListener('assignmentDeleted', handleAssignmentUpdate);
        window.removeEventListener('newAssignmentCreated', handleAssignmentUpdate);
        
        window.removeEventListener('materialUpdated', handleMaterialUpdate);
        window.removeEventListener('materialDeleted', handleMaterialUpdate);
        window.removeEventListener('newMaterialCreated', handleMaterialUpdate);
      };
    }
  }, [classId]);

  // Update expanded topics on assignments/materials change
  useEffect(() => {
    const allTopics = Object.keys(itemsByTopic);
    setExpandedTopics((prev) => {
      const nextState = { ...prev };
      allTopics.forEach((topic) => {
        if (!(topic in nextState)) nextState[topic] = true;
      });
      // Remove topics that no longer exist
      Object.keys(nextState).forEach((topic) => {
        if (!allTopics.includes(topic)) delete nextState[topic];
      });
      return nextState;
    });
  }, [assignments, materials]);

  const toggleTopic = (topic: string) => {
    setExpandedTopics((prev) => ({ ...prev, [topic]: !prev[topic] }));
  };

  // Function to load assignments
  const loadAssignments = async () => {
    if (classId) {
      try {
        const classAssignments = await getClassAssignments(classId);
        setAssignments(classAssignments);
      } catch (error) {
        console.error('Error loading assignments:', error);
        setAssignments([]);
      }
    }
  };
  
  // Function to load materials
  const loadMaterials = async () => {
    if (classId) {
      try {
        const classMaterials = await getClassMaterials(classId);
        setMaterials(classMaterials);
      } catch (error) {
        console.error('Error loading materials:', error);
        setMaterials([]);
      }
    }
  };
  
  // Group assignments and materials by topic
  const itemsByTopic: { [key: string]: (Assignment | Material)[] } = {};
  
  // Add assignments to topics
  assignments.forEach(assignment => {
    const topic = assignment.topic || 'No topic';
    if (!itemsByTopic[topic]) {
      itemsByTopic[topic] = [];
    }
    itemsByTopic[topic].push(assignment);
  });
  
  // Add materials to topics
  materials.forEach(material => {
    const topic = material.topic || 'No topic';
    if (!itemsByTopic[topic]) {
      itemsByTopic[topic] = [];
    }
    itemsByTopic[topic].push(material);
  });
  
  // Sort topics to ensure consistent order
  const sortedTopics = Object.keys(itemsByTopic).sort();

  // Update document title when class data changes
  useEffect(() => {
    const className = classData.name || 'Class';
    const section = classData.section ? ` - ${classData.section}` : '';
    document.title = `${className}${section} - Classwork - Google Classroom`;
  }, [classData]);

  // Check if we're coming back from a submission page
  useEffect(() => {
    if (location.state && location.state.fromAssignment) {
      const assignmentId = location.state.fromAssignment;
      // Find the assignment in our list to potentially highlight it
      const assignmentElement = document.getElementById(`assignment-${assignmentId}`);
      if (assignmentElement) {
        assignmentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [location.state]);

  // No need to save assignments to localStorage anymore as it's handled by our utility functions

  const closeAllForms = () => {
    setShowAssignmentForm(false);
    setShowQuizForm(false);
    setShowQuestionForm(false);
    setShowMaterialForm(false);
    setShowSelectClassModal(false);
    setShowReusePostModal(false);
    setShowAddTopicModal(false);
    setIsCreateMenuOpen(false);
  };

  const handleSelectClass = () => {
    setShowSelectClassModal(false);
    setShowReusePostModal(true);
  };

  const handleReusePost = () => {
    // Handle reusing the post here
    setShowReusePostModal(false);
    // You would typically make an API call to copy the post to the current class
  };

  const handleAddTopic = (newTopic: string) => {
    // Handle adding the new topic here
    console.log('New topic:', newTopic);
  };

  const handleAssignmentSubmit = useCallback((assignmentData: AssignmentData) => {
    console.log('Assignment data:', assignmentData);
    
    // The actual saving is now handled in the AssignmentModal component
    // using our utility functions, so we just need to close the forms
    closeAllForms();
    
    // Reload assignments to reflect changes
    loadAssignments();
  }, [closeAllForms]);
  
  // No need for this effect anymore as we're using our utility functions
  

  const handleEditAssignment = (id: string) => {
    setCurrentEditingAssignment(id);
    setShowAssignmentForm(true);
  };

  const handleDeleteAssignment = async (id: string) => {
    if (!id) {
      console.error('Cannot delete assignment: ID is undefined');
      return;
    }

    console.log('Deleting assignment with ID:', id);
    
    try {
      // Delete the assignment using our utility function
      await deleteAssignment(id);
      
      // Reload assignments after deletion
      loadAssignments();
    } catch (error) {
      console.error('Error deleting assignment:', error);
    }
  };

  const handleEditMaterial = (id: string) => {
    setCurrentEditingMaterial(id);
    setShowMaterialForm(true);
  };

  const handleDeleteMaterial = async (id: string) => {
    if (!id) {
      console.error('Cannot delete material: ID is undefined');
      return;
    }

    console.log('Deleting material with ID:', id);
    
    try {
      // Delete the material using our utility function
      await deleteMaterial(id);
      
      // Reload materials after deletion
      loadMaterials();
    } catch (error) {
      console.error('Error deleting material:', error);
    }
  };

  // Get the current assignment being edited
  const getCurrentAssignment = () => {
    if (!currentEditingAssignment) return null;
    return assignments.find(a => a.id === currentEditingAssignment) || null;
  };

  // Get the current material being edited
  const getCurrentMaterial = () => {
    if (!currentEditingMaterial) return null;
    return materials.find(m => m.id === currentEditingMaterial) || null;
  };

  // Handle material submission
  const handleMaterialSubmit = useCallback((materialData: MaterialData) => {
    console.log('Material data:', materialData);
    
    // The actual saving is handled in the MaterialModal component
    // using our utility functions, so we just need to close the forms
    closeAllForms();
    
    // Reload materials to reflect changes
    loadMaterials();
  }, [closeAllForms]);

  return (
    <div className="max-w-[1000px] mx-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">

      {/* Create Button */}
      <div className="relative mb-4 sm:mb-6">
        <button
          onClick={() => setIsCreateMenuOpen(!isCreateMenuOpen)}
          className="flex items-center gap-1 sm:gap-2 px-4 sm:px-6 py-1.5 sm:py-2 bg-[#1a73e8] text-white rounded-full text-xs sm:text-sm font-medium hover:bg-[#1557b0] transition-colors"
        >
          <Plus size={16} className="sm:w-5 sm:h-5" />
          Create
        </button>

        {/* Create Menu Dropdown */}
        {isCreateMenuOpen && (
          <div className="absolute top-full left-0 mt-1 w-56 sm:w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
            <button 
              onClick={() => {
                setCurrentEditingAssignment(null); // Ensure we're creating a new assignment
                setShowAssignmentForm(true);
                setShowQuizForm(false);
                setShowQuestionForm(false);
                setShowMaterialForm(false);
                setIsCreateMenuOpen(false);
              }}
              className="w-full px-3 sm:px-4 py-2 flex items-center gap-2 sm:gap-3 hover:bg-gray-50 text-xs sm:text-sm"
            >
              <FileText size={16} className="sm:w-5 sm:h-5 text-gray-600" />
              Assignment
            </button>
            <button 
              onClick={() => {
                setShowMaterialForm(true);
                setShowAssignmentForm(false);
                setShowQuizForm(false);
                setShowQuestionForm(false);
                setIsCreateMenuOpen(false);
              }}
              className="w-full px-3 sm:px-4 py-2 flex items-center gap-2 sm:gap-3 hover:bg-gray-50 text-xs sm:text-sm"
            >
              <FileText size={16} className="sm:w-5 sm:h-5 text-gray-600" />
              Material
            </button>
          </div>
        )}
      </div>

      {/* Assignment Modal */}
      <AssignmentModal 
        isOpen={showAssignmentForm}
        onClose={() => {
          setShowAssignmentForm(false);
          setCurrentEditingAssignment(null);
        }}
        onSubmit={handleAssignmentSubmit}
        className={classData.name}
        assignmentToEdit={getCurrentAssignment()}
      />

      {/* Material Modal */}
      <MaterialModal 
        isOpen={showMaterialForm}
        onClose={() => {
          setShowMaterialForm(false);
          setCurrentEditingMaterial(null);
        }}
        onSubmit={handleMaterialSubmit}
        className={classData.name}
        materialToEdit={getCurrentMaterial()}
      />

      {/* Classwork List or Empty State */}
      {(assignments.length > 0 || materials.length > 0) ? (
        <div className="space-y-4 sm:space-y-6">
          {sortedTopics.map((topic) => (
            <div key={topic} className="border border-gray-200 rounded-lg overflow-hidden">
              {/* Topic Header */}
              <div 
                className="flex justify-between items-center p-3 sm:p-4 bg-white cursor-pointer"
                onClick={() => toggleTopic(topic)}
              >
                <div>
                  <h2 className="text-base sm:text-lg md:text-xl font-medium">{topic}</h2>
                </div>
                <button className="p-1.5 sm:p-2 hover:bg-gray-100 rounded-full" onClick={e => { e.stopPropagation(); toggleTopic(topic); }}>
                  {expandedTopics[topic] ? (
                    <ChevronUp size={18} className="sm:w-5 sm:h-5 text-gray-500" />
                  ) : (
                    <ChevronDown size={18} className="sm:w-5 sm:h-5 text-gray-500" />
                  )}
                </button>
              </div>
              {expandedTopics[topic] && (
                <div className="p-3 sm:p-4 space-y-3 sm:space-y-4 bg-white">
                  {itemsByTopic[topic].map((item, index) => {
                    // Check if the item is an assignment (has 'instructions' property)
                    if ('instructions' in item) {
                      const assignment = item as Assignment;
                      return (
                        <AssignmentCard 
                          key={assignment.id ? `assignment-${assignment.id}` : `temp-assignment-${index}`}
                          id={assignment.id}
                          title={assignment.title}
                          description={assignment.description}
                          instructions={assignment.instructions}
                          points={assignment.points}
                          dueDate={assignment.dueDate ? assignment.dueDate : ''}
                          onEdit={handleEditAssignment}
                          onDelete={handleDeleteAssignment}
                        />
                      );
                    } else {
                      // It's a material
                      const material = item as Material;
                      return (
                        <MaterialCard 
                          key={material.id ? `material-${material.id}` : `temp-material-${index}`}
                          id={material.id}
                          title={material.title}
                          description={material.description}
                          attachments={material.attachments}
                          onEdit={handleEditMaterial}
                          onDelete={handleDeleteMaterial}
                        />
                      );
                    }
                  })}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        // Empty State
        !showAssignmentForm && !showQuizForm && !showQuestionForm && !showMaterialForm && (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="flex flex-col items-center py-8 sm:py-12 md:py-16 px-4">
              <img
                src="/classwork-empty.svg"
                alt="Empty classwork"
                className="w-[160px] h-[160px] sm:w-[200px] sm:h-[200px] md:w-[240px] md:h-[240px] mb-4 sm:mb-6"
              />
              {location.state && location.state.fromAssignment ? (
                <>
                  <h2 className="text-lg sm:text-xl md:text-[22px] text-[#3c4043] font-normal mb-1 text-center">
                    The assignment you were viewing could not be found
                  </h2>
                  <p className="text-[#5f6368] text-xs sm:text-sm md:text-[14px] text-center">
                    Create a new assignment by clicking the Create button above
                  </p>
                </>
              ) : (
                <>
                  <h2 className="text-lg sm:text-xl md:text-[22px] text-[#3c4043] font-normal mb-1 text-center">
                    This is where you'll assign work
                  </h2>
                  <p className="text-[#5f6368] text-xs sm:text-sm md:text-[14px] text-center">
                    You can add assignments and other work for the class, then organize it into topics
                  </p>
                </>
              )}
            </div>
          </div>
        )
      )}

      {/* Quiz and Question Forms would go here */}

      {/* Quiz Assignment Form */}
      {showQuizForm && (
        <div className="fixed inset-0 bg-black/50 flex items-start justify-center pt-16 z-50">
          <div className="bg-white w-full max-w-[1000px] rounded-lg h-[calc(100vh-100px)] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b sticky top-0 bg-white z-10">
              <div className="flex items-center gap-3">
                <button onClick={() => setShowQuizForm(false)}>
                  <X size={24} className="text-[#5f6368]" />
                </button>
                <div className="flex items-center gap-3">
                  <FileText className="text-[#5f6368]" size={24} />
                  <h1 className="text-[32px] text-[#3c4043] font-normal">Assignment</h1>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button className="px-6 py-2 text-sm text-[#444746] hover:bg-[#f8f9fa] rounded">
                  Cancel
                </button>
                <button className="px-6 py-2 text-sm bg-[#1a73e8] text-white rounded hover:bg-[#1557b0] font-medium">
                  Assign
                </button>
              </div>
            </div>

            <div className="flex p-6">
              {/* Left side - Assignment form */}
              <div className="flex-1 pr-6">
                <div className="space-y-4 sm:space-y-6">
                  <input
                    type="text"
                    placeholder="Title"
                    className="w-full px-3 py-4 text-[#3c4043] placeholder-[#5f6368] bg-[#f8f9fa] rounded-t border-b border-[#e0e0e0] focus:outline-none text-[16px]"
                  />

                  <div className="bg-[#f8f9fa] p-4 rounded">
                    <textarea
                      placeholder="Instructions (optional)"
                      className="w-full min-h-[100px] bg-transparent placeholder-[#5f6368] focus:outline-none resize-none text-[14px]"
                    />
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center gap-1">
                        <button className="p-2 hover:bg-[#edf2fa] rounded">
                          <span className="font-bold">B</span>
                        </button>
                        <button className="p-2 hover:bg-[#edf2fa] rounded">
                          <span className="italic">I</span>
                        </button>
                        <button className="p-2 hover:bg-[#edf2fa] rounded">
                          <span className="underline">U</span>
                        </button>
                        <button className="p-2 hover:bg-[#edf2fa] rounded">
                          <span className="text-[#5f6368]">≡</span>
                        </button>
                      </div>
                      <div className="flex items-center gap-2">
                        <button className="p-2 hover:bg-[#edf2fa] rounded-full">
                          <Upload size={20} className="text-[#5f6368]" />
                        </button>
                        <button className="p-2 hover:bg-[#edf2fa] rounded-full">
                          <LinkIcon size={20} className="text-[#5f6368]" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Quiz Form */}
                  <div className="bg-white border border-[#e0e0e0] rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-4">
                      <img src="https://www.gstatic.com/images/branding/product/1x/forms_48dp.png" alt="Google Forms" className="w-6 h-6" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-[#3c4043]">Blank Quiz</span>
                          <button className="text-[#1a73e8] text-sm hover:bg-[#f6fafe] px-3 py-1 rounded">
                            Change
                          </button>
                        </div>
                        <span className="text-xs text-[#5f6368]">Google Forms</span>
                      </div>
                      <button className="p-1 hover:bg-[#f8f9fa] rounded">
                        <X size={20} className="text-[#5f6368]" />
                      </button>
                    </div>
                    <button className="w-full text-left text-sm text-[#1a73e8] hover:bg-[#f6fafe] px-3 py-2 rounded">
                      + Add question
                    </button>
                  </div>

                  {/* Attach Section */}
                  <div>
                    <h3 className="text-sm text-[#3c4043] mb-4">Attach</h3>
                    <div className="flex gap-4">
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <img src="/drive-icon.svg" alt="Drive" className="w-6 h-6" />
                        <span className="text-xs text-[#5f6368]">Drive</span>
                      </button>
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <img src="/youtube-icon.svg" alt="YouTube" className="w-6 h-6" />
                        <span className="text-xs text-[#5f6368]">YouTube</span>
                      </button>
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <Plus size={24} className="text-[#5f6368]" />
                        <span className="text-xs text-[#5f6368]">Create</span>
                      </button>
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <img src="/upload-icon.svg" alt="Upload" className="w-6 h-6" />
                        <span className="text-xs text-[#5f6368]">Upload</span>
                      </button>
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <img src="/link-icon.svg" alt="Link" className="w-6 h-6" />
                        <span className="text-xs text-[#5f6368]">Link</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right side - Assignment settings */}
              <div className="w-[300px] space-y-4">
                <div className="bg-white border border-[#e0e0e0] rounded-lg p-4">
                  <h3 className="text-sm font-medium text-[#3c4043] mb-4">For</h3>
                  <button className="w-full px-3 py-2 text-sm border rounded hover:bg-[#f8f9fa] flex items-center justify-between">
                    <span>{classData.className}</span>
                    <ChevronDown size={16} className="text-[#5f6368]" />
                  </button>
                  <button className="mt-3 w-full px-3 py-2 text-sm border rounded hover:bg-[#f8f9fa] flex items-center gap-2 text-[#1a73e8]">
                    <Users size={16} />
                    All students
                  </button>
                </div>

                <div className="bg-white border border-[#e0e0e0] rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-[#3c4043]">Points</h3>
                    <button className="text-[#1a73e8] text-sm hover:bg-[#f6fafe] px-2 py-1 rounded">
                      100
                    </button>
                  </div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-[#3c4043]">Due</h3>
                    <button className="text-[#1a73e8] text-sm hover:bg-[#f6fafe] px-2 py-1 rounded">
                      No due date
                    </button>
                  </div>
                </div>

                <div>
                  <button className="text-[#1a73e8] text-sm hover:bg-[#f6fafe] px-4 py-2 rounded flex items-center gap-1">
                    <Plus size={16} />
                    Rubric
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Question Form */}
      {showQuestionForm && (
        <div className="fixed inset-0 bg-black/50 flex items-start justify-center pt-16 z-50">
          <div className="bg-white w-full max-w-[1000px] rounded-lg h-[calc(100vh-100px)] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b sticky top-0 bg-white z-10">
              <div className="flex items-center gap-3">
                <button onClick={() => setShowQuestionForm(false)}>
                  <X size={24} className="text-[#5f6368]" />
                </button>
                <div className="flex items-center gap-3">
                  <HelpCircle className="text-[#5f6368]" size={24} />
                  <h1 className="text-[32px] text-[#3c4043] font-normal">Question</h1>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button className="px-6 py-2 text-sm text-[#444746] hover:bg-[#f8f9fa] rounded">
                  Cancel
                </button>
                <button className="px-6 py-2 text-sm bg-[#1a73e8] text-white rounded hover:bg-[#1557b0] font-medium">
                  Ask
                </button>
              </div>
            </div>

            <div className="flex p-6">
              {/* Left side - Question form */}
              <div className="flex-1 pr-6">
                <div className="space-y-4 sm:space-y-6">
                  <div className="flex gap-6">
                    <input
                      type="text"
                      placeholder="Question"
                      className="flex-1 px-3 py-4 text-[#3c4043] placeholder-[#5f6368] bg-[#f8f9fa] rounded-t border-b border-[#e0e0e0] focus:outline-none text-[16px]"
                    />
                    <button className="w-[200px] px-3 py-4 text-[#3c4043] bg-[#f8f9fa] rounded-t border-b border-[#e0e0e0] flex items-center justify-between">
                      <span className="text-sm">Short answer</span>
                      <ChevronDown size={16} className="text-[#5f6368]" />
                    </button>
                  </div>

                  <div className="bg-[#f8f9fa] p-4 rounded">
                    <textarea
                      placeholder="Instructions (optional)"
                      className="w-full min-h-[100px] bg-transparent placeholder-[#5f6368] focus:outline-none resize-none text-[14px]"
                    />
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center gap-1">
                        <button className="p-2 hover:bg-[#edf2fa] rounded">
                          <span className="font-bold">B</span>
                        </button>
                        <button className="p-2 hover:bg-[#edf2fa] rounded">
                          <span className="italic">I</span>
                        </button>
                        <button className="p-2 hover:bg-[#edf2fa] rounded">
                          <span className="underline">U</span>
                        </button>
                        <button className="p-2 hover:bg-[#edf2fa] rounded">
                          <span className="text-[#5f6368]">≡</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Attach Section */}
                  <div>
                    <h3 className="text-sm text-[#3c4043] mb-4">Attach</h3>
                    <div className="flex gap-4">
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <img src="/drive-icon.svg" alt="Drive" className="w-6 h-6" />
                        <span className="text-xs text-[#5f6368]">Drive</span>
                      </button>
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <img src="/youtube-icon.svg" alt="YouTube" className="w-6 h-6" />
                        <span className="text-xs text-[#5f6368]">YouTube</span>
                      </button>
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <Plus size={24} className="text-[#5f6368]" />
                        <span className="text-xs text-[#5f6368]">Create</span>
                      </button>
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <img src="/upload-icon.svg" alt="Upload" className="w-6 h-6" />
                        <span className="text-xs text-[#5f6368]">Upload</span>
                      </button>
                      <button className="flex flex-col items-center gap-1 p-4 hover:bg-[#f8f9fa] rounded">
                        <img src="/link-icon.svg" alt="Link" className="w-6 h-6" />
                        <span className="text-xs text-[#5f6368]">Link</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right side - Question settings */}
              <div className="w-[300px] space-y-4">
                <div className="bg-white border border-[#e0e0e0] rounded-lg p-4">
                  <h3 className="text-sm font-medium text-[#3c4043] mb-4">For</h3>
                  <button className="w-full px-3 py-2 text-sm border rounded hover:bg-[#f8f9fa] flex items-center justify-between">
                    <span>{classData.className}</span>
                    <ChevronDown size={16} className="text-[#5f6368]" />
                  </button>
                  <button className="mt-3 w-full px-3 py-2 text-sm border rounded hover:bg-[#f8f9fa] flex items-center gap-2 text-[#1a73e8]">
                    <Users size={16} />
                    All students
                  </button>
                </div>

                <div className="bg-white border border-[#e0e0e0] rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-[#3c4043]">Points</h3>
                    <button className="text-[#1a73e8] text-sm hover:bg-[#f6fafe] px-2 py-1 rounded">
                      100
                    </button>
                  </div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-[#3c4043]">Due</h3>
                    <button className="text-[#1a73e8] text-sm hover:bg-[#f6fafe] px-2 py-1 rounded">
                      No due date
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={studentsCanReply}
                      onChange={(e) => setStudentsCanReply(e.target.checked)}
                      className="form-checkbox text-[#1a73e8] rounded"
                    />
                    <span className="text-sm text-[#3c4043]">Students can reply to each other</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={studentsCanEdit}
                      onChange={(e) => setStudentsCanEdit(e.target.checked)}
                      className="form-checkbox text-[#1a73e8] rounded"
                    />
                    <span className="text-sm text-[#3c4043]">Students can edit answer</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Select Class Modal */}
      <SelectClassModal
        isOpen={showSelectClassModal}
        onClose={() => setShowSelectClassModal(false)}
        onSelectClass={handleSelectClass}
      />

      {/* Reuse Post Modal */}
      <ReusePostModal
        isOpen={showReusePostModal}
        onClose={() => setShowReusePostModal(false)}
        selectedClass={classData.name || ''}
        onReuse={handleReusePost}
      />

      {/* Add Topic Modal */}
      <AddTopicModal
        isOpen={showAddTopicModal}
        onClose={() => setShowAddTopicModal(false)}
        onAdd={handleAddTopic}
      />
    </div>
  );
};

export default ClassworkPage;