import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface Student {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  assignmentAvg?: string;
  participation?: string;
  finalGrade?: string;
  finalGradeColor?: string;
}

interface Submission {
  id: string;
  studentName: string;
  studentId: string;
  status: 'submitted' | 'late' | 'missing' | 'graded';
  submittedDate?: string;
  grade?: number;
  letterGrade?: string;
  gradePercentage?: number;
  feedback?: string;
}

interface StudentDataContextType {
  students: Student[];
  submissions: Submission[];
  addStudent: (student: Student) => void;
  updateStudent: (id: string, data: Partial<Student>) => void;
  removeStudent: (id: string) => void;
  updateSubmission: (submissionId: string, data: Partial<Submission>) => void;
  getStudentById: (id: string) => Student | undefined;
  getSubmissionsByStudentId: (studentId: string) => Submission[];
  syncGradeData: () => void;
}

const StudentDataContext = createContext<StudentDataContextType | undefined>(undefined);

export const useStudentData = () => {
  const context = useContext(StudentDataContext);
  if (context === undefined) {
    throw new Error('useStudentData must be used within a StudentDataProvider');
  }
  return context;
};

interface StudentDataProviderProps {
  children: ReactNode;
}

export const StudentDataProvider: React.FC<StudentDataProviderProps> = ({ children }) => {
  // Load data from localStorage or initialize with samples on first load
  useEffect(() => {
    const loadedStudents = localStorage.getItem('students');
    const loadedSubmissions = localStorage.getItem('submissions');
    
    if (loadedStudents && loadedSubmissions) {
      try {
        setStudents(JSON.parse(loadedStudents));
        setSubmissions(JSON.parse(loadedSubmissions));
      } catch (e) {
        console.error('Error parsing students or submissions from localStorage', e);
        initializeWithSampleData();
      }
    } else {
      initializeWithSampleData();
    }
  }, []);

  // Initialize students with empty array
  const [students, setStudents] = useState<Student[]>([]);

  // Initialize submissions with empty array  
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  
  // Function to initialize with sample data
  const initializeWithSampleData = () => {
    const sampleStudents: Student[] = [
      {
        id: 'student-1',
        name: 'Alex Johnson',
        email: '<EMAIL>',
        assignmentAvg: '92.5%',
        participation: '100%',
        finalGrade: '92.5%',
      },
      {
        id: 'student-2',
        name: 'Jamie Smith',
        email: '<EMAIL>',
        assignmentAvg: '85.0%',
        participation: '90%',
        finalGrade: '85.0%',
      },
      {
        id: 'student-3',
        name: 'Morgan Lee',
        email: '<EMAIL>',
        assignmentAvg: '78.4%',
        participation: '80%',
        finalGrade: '78.4%',
      },
      {
        id: 'student-4',
        name: 'Taylor Brooks',
        email: '<EMAIL>',
        assignmentAvg: '94.7%',
        participation: '100%',
        finalGrade: '94.7%',
      }
    ];
    
    // Create sample submissions for these students
    const sampleSubmissions: Submission[] = [
      {
        id: 'sub-student-1-1',
        studentName: 'Alex Johnson',
        studentId: 'student-1',
        status: 'graded',
        submittedDate: '2025-05-05T10:15:00Z',
        grade: 92,
        letterGrade: 'A-',
        gradePercentage: 92
      },
      {
        id: 'sub-student-2-1',
        studentName: 'Jamie Smith',
        studentId: 'student-2',
        status: 'graded',
        submittedDate: '2025-05-05T09:30:00Z',
        grade: 85,
        letterGrade: 'B',
        gradePercentage: 85
      },
      {
        id: 'sub-student-3-1',
        studentName: 'Morgan Lee',
        studentId: 'student-3',
        status: 'graded',
        submittedDate: '2025-05-05T11:45:00Z',
        grade: 78,
        letterGrade: 'C+',
        gradePercentage: 78
      },
      {
        id: 'sub-student-4-1',
        studentName: 'Taylor Brooks',
        studentId: 'student-4',
        status: 'graded',
        submittedDate: '2025-05-05T10:00:00Z',
        grade: 95,
        letterGrade: 'A',
        gradePercentage: 95
      }
    ];
    
    setStudents(sampleStudents);
    setSubmissions(sampleSubmissions);
    
    // Save to localStorage
    localStorage.setItem('students', JSON.stringify(sampleStudents));
    localStorage.setItem('submissions', JSON.stringify(sampleSubmissions));
    
    // Trigger grade sync event
    const gradeUpdateEvent = new CustomEvent('gradesUpdated');
    window.dispatchEvent(gradeUpdateEvent);
  };

  // Save students to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('students', JSON.stringify(students));
  }, [students]);

  // Save submissions to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('submissions', JSON.stringify(submissions));
  }, [submissions]);

  // Listen for grade updates from StudentSubmissionPage
  useEffect(() => {
    const handleGradeUpdate = () => {
      const gradedSubmissionJson = localStorage.getItem('gradedSubmission');
      if (gradedSubmissionJson) {
        try {
          const gradedSubmission = JSON.parse(gradedSubmissionJson);
          
          // Update the submissions list with graded submission
          setSubmissions(prevSubmissions =>
            prevSubmissions.map(sub =>
              sub.id === gradedSubmission.id ?
              {
                ...sub,
                status: 'graded',
                grade: gradedSubmission.grade,
                letterGrade: gradedSubmission.letterGrade,
                gradePercentage: gradedSubmission.gradePercentage
              } :
              sub
            )
          );
          
          // Also update the student's grade data
          syncGradeData();
          
          // Remove the temporary storage
          localStorage.removeItem('gradedSubmission');
        } catch (e) {
          console.error('Error parsing graded submission', e);
        }
      }
    };

    // Check for graded submissions when component mounts
    handleGradeUpdate();

    // Listen for the custom event that signals a new assignment was created or updated
    window.addEventListener('newAssignmentCreated', handleGradeUpdate);
    
    return () => {
      window.removeEventListener('newAssignmentCreated', handleGradeUpdate);
    };
  }, []);

  // Add a new student
  const addStudent = (student: Student) => {
    setStudents(prevStudents => [...prevStudents, student]);
    
    // Create an empty submission for this student for all existing assignments
    // This is a simplified approach - in a real app, you'd need to create submissions for each assignment
    const newSubmission: Submission = {
      id: `sub-${student.id}-${Date.now()}`,
      studentName: student.name,
      studentId: student.id,
      status: 'missing'
    };
    
    setSubmissions(prevSubmissions => [...prevSubmissions, newSubmission]);
    
    // Dispatch event to notify other components
    const newStudentEvent = new CustomEvent('studentDataUpdated', {
      detail: { type: 'add', studentId: student.id }
    });
    window.dispatchEvent(newStudentEvent);
  };

  // Update an existing student
  const updateStudent = (id: string, data: Partial<Student>) => {
    setStudents(prevStudents =>
      prevStudents.map(student =>
        student.id === id ? { ...student, ...data } : student
      )
    );
    
    // If the student's name was updated, update it in all their submissions too
    if (data.name) {
      setSubmissions(prevSubmissions =>
        prevSubmissions.map(submission =>
          submission.studentId === id ? { ...submission, studentName: data.name as string } : submission
        )
      );
    }
    
    // Dispatch event to notify other components
    const updateStudentEvent = new CustomEvent('studentDataUpdated', {
      detail: { type: 'update', studentId: id }
    });
    window.dispatchEvent(updateStudentEvent);
  };

  // Remove a student
  const removeStudent = (id: string) => {
    setStudents(prevStudents => prevStudents.filter(student => student.id !== id));
    
    // Also remove all submissions from this student
    setSubmissions(prevSubmissions => prevSubmissions.filter(submission => submission.studentId !== id));
    
    // Dispatch event to notify other components
    const removeStudentEvent = new CustomEvent('studentDataUpdated', {
      detail: { type: 'remove', studentId: id }
    });
    window.dispatchEvent(removeStudentEvent);
  };

  // Update a submission
  const updateSubmission = (submissionId: string, data: Partial<Submission>) => {
    setSubmissions(prevSubmissions =>
      prevSubmissions.map(submission =>
        submission.id === submissionId ? { ...submission, ...data } : submission
      )
    );
    
    // If this is a grade update, sync with student data
    if (data.grade !== undefined || data.status === 'graded') {
      syncGradeData();
    }
    
    // Dispatch event to notify other components
    const updateSubmissionEvent = new CustomEvent('submissionUpdated', {
      detail: { submissionId }
    });
    window.dispatchEvent(updateSubmissionEvent);
  };

  // Get a student by ID
  const getStudentById = (id: string) => {
    return students.find(student => student.id === id);
  };

  // Get all submissions for a student
  const getSubmissionsByStudentId = (studentId: string) => {
    return submissions.filter(submission => submission.studentId === studentId);
  };

  // Sync grades between submissions and student data
  const syncGradeData = () => {
    // For each student, calculate their average grade from all submissions
    const updatedStudents = students.map(student => {
      const studentSubmissions = submissions.filter(sub => sub.studentId === student.id);
      const gradedSubmissions = studentSubmissions.filter(sub => sub.status === 'graded' && sub.grade !== undefined);
      
      // Calculate participation based on submission status
      // Participation is the percentage of assignments that were submitted (not missing)
      const totalSubmissions = studentSubmissions.length;
      const submittedCount = studentSubmissions.filter(sub => 
        sub.status === 'submitted' || sub.status === 'late' || sub.status === 'graded'
      ).length;
      
      // Calculate participation percentage
      const participationPercentage = totalSubmissions > 0 
        ? Math.round((submittedCount / totalSubmissions) * 100) + '%' 
        : '0%';
      
      // If the student has no graded submissions, return with updated participation only
      if (gradedSubmissions.length === 0) {
        return {
          ...student,
          participation: participationPercentage,
          // Keep existing grades or set to 0% if undefined
          assignmentAvg: student.assignmentAvg || '0%',
          finalGrade: student.finalGrade || '0%',
          finalGradeColor: student.finalGradeColor || 'text-red-600'
        };
      }
      
      // Calculate average grade
      const totalPoints = gradedSubmissions.reduce((sum, sub) => sum + (sub.grade || 0), 0);
      const avgGrade = totalPoints / gradedSubmissions.length;
      const avgPercentage = (avgGrade).toFixed(1) + '%';
      
      // Determine grade color based on average
      let finalGradeColor = 'text-red-600';
      if (avgGrade >= 90) finalGradeColor = 'text-green-600';
      else if (avgGrade >= 80) finalGradeColor = 'text-blue-600';
      else if (avgGrade >= 70) finalGradeColor = 'text-yellow-600';
      else if (avgGrade >= 60) finalGradeColor = 'text-orange-600';
      
      // Update the student with new grade data
      return {
        ...student,
        assignmentAvg: avgPercentage,
        participation: participationPercentage,
        finalGrade: avgPercentage,
        finalGradeColor
      };
    });
    
    setStudents(updatedStudents);
    
    // Dispatch event to notify other components about the grade update
    const gradeUpdateEvent = new CustomEvent('gradesUpdated');
    window.dispatchEvent(gradeUpdateEvent);
  };

  const value = {
    students,
    submissions,
    addStudent,
    updateStudent,
    removeStudent,
    updateSubmission,
    getStudentById,
    getSubmissionsByStudentId,
    syncGradeData
  };

  return (
    <StudentDataContext.Provider value={value}>
      {children}
    </StudentDataContext.Provider>
  );
};

export type { Student, Submission };

export default StudentDataContext;