import React, { useEffect } from 'react';
import { useAuthStore } from '../stores/useAuthStore';
import { getCurrentUser } from '../api/authApi';

export const AuthDebugger: React.FC = () => {
  const user = useAuthStore((state) => state.user);
  const isLoading = useAuthStore((state) => state.isLoading);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const loadUser = useAuthStore((state) => state.loadUser);

  useEffect(() => {
    console.log('=== AUTH DEBUGGER ===');
    console.log('User object:', user);
    console.log('User properties:', {
      name: user?.name,
      role: user?.role,
      userId: user?.userId,
      id: user?.id,
      email: user?.email
    });
    console.log('IsLoading:', isLoading);
    console.log('IsAuthenticated:', isAuthenticated);
    console.log('Token in sessionStorage:', !!sessionStorage.getItem('auth_token'));
    console.log('Token in localStorage:', !!localStorage.getItem('auth_token'));
    console.log('User role in sessionStorage:', sessionStorage.getItem('user_role'));
    console.log('User role in localStorage:', localStorage.getItem('user_role'));
    console.log('====================');
  }, [user, isLoading, isAuthenticated]);

  const handleManualLoad = () => {
    console.log('Manually triggering loadUser...');
    loadUser();
  };

  const handleDirectApiTest = async () => {
    console.log('Testing getCurrentUser API directly...');
    try {
      const result = await getCurrentUser();
      console.log('Direct API call result:', result);
    } catch (error) {
      console.error('Direct API call error:', error);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h4>Auth Debug</h4>
      <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
      <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
      <div>User Object: {user ? 'Present' : 'null'}</div>
      <div>User Name: {user?.name || 'undefined'}</div>
      <div>User Role: {user?.role || 'undefined'}</div>
      <div>User ID: {user?.userId || user?.id || 'undefined'}</div>
      <div>User Email: {user?.email || 'undefined'}</div>
      <div>Token: {sessionStorage.getItem('auth_token') ? 'Present' : 'Missing'}</div>
      <button
        onClick={handleManualLoad}
        style={{
          marginTop: '5px',
          padding: '5px 10px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '3px',
          cursor: 'pointer',
          marginRight: '5px'
        }}
      >
        Reload User
      </button>
      <button
        onClick={handleDirectApiTest}
        style={{
          marginTop: '5px',
          padding: '5px 10px',
          background: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '3px',
          cursor: 'pointer'
        }}
      >
        Test API
      </button>
    </div>
  );
};
